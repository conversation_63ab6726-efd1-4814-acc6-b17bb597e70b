/**
 * JavaScript модул за управление на изображения в продуктовата форма
 */
(function() {
    'use strict';

    // Разширяване на основния модул
    document.addEventListener('DOMContentLoaded', function() {
        // Инициализиране на функционалността за изображения
        if (typeof BackendModule !== 'undefined') {
            BackendModule.initProductImages();
        }
    });

    // Добавяне на функционалност към основния модул
    if (typeof BackendModule !== 'undefined') {
        Object.assign(BackendModule, {
            // Конфигурация за мениджъра на изображения
            imageManager: {
                currentDirectory: '',
                selectedImages: [],
                isOpen: false,
                lastUploadDirectory: '',
                lastSelectedElement: null,
                allImageElements: [],
                searchQuery: '',
                allItems: [],
                filteredItems: [],
                searchTimeout: null,
                pagination: {
                    currentPage: 1,
                    limit: 30,
                    hasMore: false,
                    loading: false
                }
            },

            /**
             * Инициализация на функционалността за изображения
             */
            initProductImages: function() {
                this.initImageTabDragDrop();
                this.loadImageManagerTemplate();
                this.initImageButtons();                
            },

            /**
             * Инициализация на drag & drop функционалността в таба за изображения
             */
            initImageTabDragDrop: function() {
                const imageTab = document.getElementById('tab-images');
                if (!imageTab) return;

                // Drag & Drop events
                imageTab.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    imageTab.classList.add('drag-over');
                });

                imageTab.addEventListener('dragleave', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    // Проверяваме дали наистина напускаме таба
                    if (!imageTab.contains(e.relatedTarget)) {
                        imageTab.classList.remove('drag-over');
                    }
                });

                imageTab.addEventListener('drop', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    imageTab.classList.remove('drag-over');

                    const files = Array.from(e.dataTransfer.files);
                    if (files.length > 0) {
                        this.uploadFiles(files);
                    }
                });
            },

            /**
             * Инициализация на бутоните за изображения
             */
            initImageButtons: function() {
                // Бутон "Изберете файлове"
                const selectFilesBtn = document.querySelector('#tab-images input[type="file"]');
                if (selectFilesBtn) {
                    selectFilesBtn.addEventListener('change', (e) => {
                        const files = Array.from(e.target.files);
                        if (files.length > 0) {
                            this.uploadFiles(files);
                        }
                        e.target.value = ''; // Reset input
                    });
                }

                // Бутон за добавяне на нова снимка
                const addImageBtn = document.getElementById('additional-image-upload');
                if (addImageBtn) {
                    addImageBtn.addEventListener('change', (e) => {
                        const files = Array.from(e.target.files);
                        if (files.length > 0) {
                            this.uploadFiles(files);
                        }
                        e.target.value = ''; // Reset input
                    });
                }
            },

            /**
             * Зареждане на template за мениджъра на изображения
             */
            loadImageManagerTemplate: function() {
                // Зареждаме template-а в DOM-а ако не е зареден
                if (!document.getElementById('image-manager-modal')) {
                    fetch('index.php?route=common/imagemanager/template&user_token=' + (BackendModule.config.userToken || ''), {
                        cache: 'no-store',
                        headers: { 'Cache-Control': 'no-cache, no-store, must-revalidate', 'Pragma': 'no-cache', 'Expires': '0' }
                    })
                        .then(response => response.text())
                        .then(html => {
                            document.body.insertAdjacentHTML('beforeend', html);
                            this.initImageManagerModal();
                        })
                        .catch(error => {
                            console.error('Грешка при зареждане на template:', error);
                        });
                }
            },

            /**
             * Инициализация на модалния прозорец за мениджъра на изображения
             */
            initImageManagerModal: function() {

                // Създаваме бутон "Избери от библиотеката" ако не съществува
                this.createLibraryButton();

                const modal = document.getElementById('image-manager-modal');

                document.addEventListener('click', (e) => {
                    if (e.target.id === 'open-image-library') {
                        e.preventDefault();
                        this.openImageManager();
                    }
                });

                // Event listeners за модала
                modal.addEventListener('click', (e) => {
                    // Проверяваме дали кликът е върху съдържанието на модала (бялата част)
                    // Ако кликът е върху .bg-white елемента или негов дъщерен елемент, НЕ затваряме модала
                    if (!e.target.closest('.bg-white')) {
                        // Кликът е извън съдържанието (върху overlay фона) - затваряме модала
                        this.closeImageManager();
                        return;
                    }

                    // Използваме closest() за да намерим най-близкия родителски елемент с ID
                    // Това решава проблема с кликове върху иконки в бутоните
                    const targetElement = e.target.closest('[id]') || e.target;
                    const targetId = targetElement.id;

                    console.log('Clicked on element with ID:', targetId);

                    if (targetId === 'close-image-manager') {
                        this.closeImageManager();
                    } else if (targetId === 'navigate-up') {
                        this.navigateUp();
                    } else if (targetId === 'refresh-manager') {
                        this.refreshManager();
                    } else if (targetId === 'upload-files-btn') {
                        document.getElementById('upload-files-input').click();
                    } else if (targetId === 'clear-selection') {
                        this.clearSelection();
                    } else if (targetId === 'add-selected') {
                        this.addSelectedImages();
                    } else if (targetId === 'clear-search') {
                        this.clearSearch();
                    }
                });

                // Search functionality with debouncing
                const searchInput = document.getElementById('search-images');
                if (searchInput) {
                    searchInput.addEventListener('input', (e) => {
                        this.handleSearchWithDebounce(e.target.value);
                    });
                }

                // Infinite scroll functionality
                this.initInfiniteScroll();

                // Upload files input
                const uploadInput = document.getElementById('upload-files-input');
                if (uploadInput) {
                    uploadInput.addEventListener('change', (e) => {
                        const files = Array.from(e.target.files);
                        if (files.length > 0) {
                            this.uploadFilesToManager(files);
                        }
                        e.target.value = ''; // Reset input
                    });
                }

                // Breadcrumb navigation
                document.addEventListener('click', (e) => {
                    if (e.target.closest('.breadcrumb-item')) {
                        const path = e.target.closest('.breadcrumb-item').dataset.path;
                        this.navigateToDirectory(path);
                    }
                });

                // Drag & Drop в мениджъра
                
                
                if (modal) {
                    modal.addEventListener('dragover', (e) => {
                        e.preventDefault();
                        document.getElementById('drop-zone-overlay').classList.remove('hidden');
                    });

                    modal.addEventListener('dragleave', (e) => {
                        if (!modal.contains(e.relatedTarget)) {
                            document.getElementById('drop-zone-overlay').classList.add('hidden');
                        }
                    });

                    modal.addEventListener('drop', (e) => {
                        e.preventDefault();
                        document.getElementById('drop-zone-overlay').classList.add('hidden');
                        
                        const files = Array.from(e.dataTransfer.files);
                        if (files.length > 0) {
                            this.uploadFilesToManager(files);
                        }
                    });
                }
            },

            /**
             * Създаване на бутон "Избери от библиотеката"
             */
            createLibraryButton: function() {
                const selectFilesBtn = document.querySelector('#tab-images label');
                if (selectFilesBtn && !document.getElementById('open-image-library')) {
                    const libraryBtn = document.createElement('button');
                    libraryBtn.type = 'button';
                    libraryBtn.id = 'open-image-library';
                    libraryBtn.className = 'ml-4 px-4 py-2 bg-secondary text-white rounded-button hover:bg-secondary/90 transition-colors !rounded-button';
                    libraryBtn.innerHTML = '<i class="ri-folder-image-line mr-2"></i>Избери от библиотеката';
                    
                    selectFilesBtn.parentNode.insertBefore(libraryBtn, selectFilesBtn.nextSibling);
                }
            },

            /**
             * Отваряне на мениджъра на изображения
             */
            openImageManager: function() {
                const modal = document.getElementById('image-manager-modal');
                if (!modal) {
                    this.loadImageManagerTemplate();
                    return;
                }

                // Определяваме началната директория
                let startDirectory = this.imageManager.lastUploadDirectory || '';
                
                // Ако има качени изображения, отиваме в папката на последното
                const lastImage = this.getLastUploadedImage();
                if (lastImage) {
                    const imagePath = lastImage.path || lastImage.src || '';
                    if (imagePath) {
                        const pathParts = imagePath.split('/');
                        pathParts.shift(); // Премахваме 'https'
                        pathParts.shift(); // Премахваме '//'
                        pathParts.shift(); // Премахваме домейна
                        pathParts.shift(); // Премахваме 'image'
                        pathParts.shift(); // Премахваме 'cache'
                        pathParts.shift(); // Премахваме 'catalog'
                        pathParts.pop(); // Премахваме файла
                        startDirectory = pathParts.join('/');
                    }
                }

                this.imageManager.currentDirectory = startDirectory;
                this.imageManager.isOpen = true;
                this.imageManager.selectedImages = [];

                modal.classList.remove('hidden');
                this.loadDirectoryContents(startDirectory);
            },

            /**
             * Затваряне на мениджъра на изображения
             */
            closeImageManager: function() {
                const modal = document.getElementById('image-manager-modal');
                if (modal) {
                    modal.classList.add('hidden');
                    this.imageManager.isOpen = false;
                    this.imageManager.selectedImages = [];
                }
            },

            /**
             * Получаване на последното качено изображение
             */
            getLastUploadedImage: function() {
                const images = document.querySelectorAll('#tab-images .group img');
                return images.length > 0 ? images[images.length - 1] : null;
            },

            /**
             * Качване на файлове
             */
            uploadFiles: function(files) {
                if (!files || files.length === 0) return;

                // Валидация на файловете
                const validFiles = files.filter(file => {
                    const isImage = /image\/(jpeg|png|gif)/i.test(file.type);
                    const isValidSize = file.size <= 5 * 1024 * 1024; // 5MB
                    
                    if (!isImage) {
                        this.showAlert('error', `${file.name} не е валиден формат на изображение`);
                        return false;
                    }
                    if (!isValidSize) {
                        this.showAlert('error', `${file.name} е по-голям от 5MB`);
                        return false;
                    }
                    return true;
                });

                if (validFiles.length === 0) return;

                this.showAlert('info', 'Качване на изображения...', 0);

                // Качване на файловете
                const uploadPromises = validFiles.map(file => this.uploadSingleFile(file));

                Promise.all(uploadPromises)
                    .then(results => {
                        const successCount = results.filter(result => result.success).length;
                        if (successCount > 0) {
                            this.showAlert('success', `Успешно качени ${successCount} изображения`);
                            this.addUploadedImagesToProduct(results.filter(result => result.success));
                        }
                    })
                    .catch(error => {
                        console.error('Грешка при качване:', error);
                        this.showAlert('error', 'Възникна грешка при качване на изображенията');
                    });
            },

            /**
             * Качване на единичен файл
             */
            uploadSingleFile: function(file) {
                const formData = new FormData();
                formData.append('files[]', file);

                const userToken = BackendModule.config.userToken || '';
                const directory = this.imageManager.currentDirectory || '';

                return fetch(`index.php?route=common/imagemanager/upload&user_token=${userToken}&directory=${encodeURIComponent(directory)}`, {
                    method: 'POST',
                    body: formData,
                    cache: 'no-store',
                    headers: { 'Cache-Control': 'no-cache, no-store, must-revalidate', 'Pragma': 'no-cache', 'Expires': '0' }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.files && data.files.length > 0) {
                        // Запазваме директорията на последното качване
                        this.imageManager.lastUploadDirectory = directory;
                        const uploadedFile = data.files[0];
                        return {
                            success: true,
                            filename: uploadedFile.path,
                            thumb: uploadedFile.thumb
                        };
                    } else {
                        const errorMsg = data.error || data.errors?.[0] || `Грешка при качване на ${file.name}`;
                        this.showAlert('error', errorMsg);
                        return { success: false };
                    }
                })
                .catch(error => {
                    console.error('Грешка при качване на файл:', error);
                    this.showAlert('error', `Възникна грешка при качване на ${file.name}`);
                    return { success: false };
                });
            },

            /**
             * Добавяне на качените изображения към продукта
             */
            addUploadedImagesToProduct: function(uploadedFiles) {
                const imagesContainer = document.querySelector('#tab-images .grid');
                if (!imagesContainer) return;

                uploadedFiles.forEach(file => {
                    if (file.success) {
                        this.addImageToProduct(file.filename, file.thumb);
                    }
                });
            },

            /**
             * Добавяне на изображение към продукта
             */
            addImageToProduct: function(filename, thumb) {
                const imagesContainer = document.querySelector('#tab-images .grid');
                if (!imagesContainer) return;

                // Намираме бутона за добавяне (последният елемент)
                const addButton = imagesContainer.querySelector('.border-dashed');

                const imageElement = document.createElement('div');
                imageElement.className = 'relative group';
                imageElement.innerHTML = `
                    <div class="aspect-square rounded-lg overflow-hidden border border-gray-200">
                        <img src="${thumb}" alt="Product image" class="w-full h-full object-cover">
                    </div>
                    <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                        <button type="button" class="p-2 bg-white rounded-full text-gray-700 hover:text-primary" data-action="view">
                            <div class="w-5 h-5 flex items-center justify-center">
                                <i class="ri-eye-line"></i>
                            </div>
                        </button>
                        <button type="button" class="p-2 bg-white rounded-full text-gray-700 hover:text-primary" data-action="edit">
                            <div class="w-5 h-5 flex items-center justify-center">
                                <i class="ri-image-edit-line"></i>
                            </div>
                        </button>
                        <button type="button" class="p-2 bg-white rounded-full text-red-500 hover:text-red-600" data-action="remove">
                            <div class="w-5 h-5 flex items-center justify-center">
                                <i class="ri-delete-bin-line"></i>
                            </div>
                        </button>
                    </div>
                    <input type="hidden" name="product_image[]" value="${filename}">
                `;

                // Вмъкваме преди бутона за добавяне
                if (addButton) {
                    imagesContainer.insertBefore(imageElement, addButton);
                } else {
                    imagesContainer.appendChild(imageElement);
                }
            },

            /**
             * Изчислява оптималния брой изображения за зареждане въз основа на размерите на модала
             */
            calculateOptimalImageCount: function() {
                const modal = document.querySelector('.modal-dialog');
                const itemsGrid = document.getElementById('items-grid');

                if (!modal || !itemsGrid) {
                    return 30; // fallback към фиксирания брой
                }

                // Получаваме размерите на модала
                const modalHeight = modal.offsetHeight;
                const availableHeight = modalHeight - 200; // отчитаме header, footer и padding

                // Приблизителни размери на един grid item (включително gap)
                const itemHeight = 120; // височина на изображение + текст + padding
                const itemWidth = 100;  // ширина на изображение + padding
                const gridGap = 8;      // gap между елементите

                // Изчисляваме колко колони се побират в ширината
                const gridWidth = itemsGrid.offsetWidth || 600; // fallback ширина
                const columnsPerRow = Math.floor(gridWidth / (itemWidth + gridGap));

                // Изчисляваме колко реда се побират във височината
                const rowsVisible = Math.floor(availableHeight / (itemHeight + gridGap));

                // Общ брой видими изображения
                const visibleImages = columnsPerRow * rowsVisible;

                // Добавяме още един ред за да се създаде scrollbar
                const optimalCount = visibleImages + columnsPerRow;

                console.log('ImageManager: Изчислен оптимален брой изображения:', {
                    modalHeight: modalHeight,
                    availableHeight: availableHeight,
                    gridWidth: gridWidth,
                    columnsPerRow: columnsPerRow,
                    rowsVisible: rowsVisible,
                    visibleImages: visibleImages,
                    optimalCount: optimalCount
                });

                // Минимум 8, максимум 24 изображения
                return Math.max(8, Math.min(24, optimalCount));
            },

            /**
             * Зареждане на съдържанието на директория
             */
            loadDirectoryContents: function(directory = '', append = false) {
                console.log('ImageManager: loadDirectoryContents() извикан с:', {
                    directory: directory,
                    append: append,
                    currentPage: this.imageManager.pagination.currentPage
                });

                const loadingIndicator = document.getElementById('loading-indicator');
                const itemsGrid = document.getElementById('items-grid');
                const emptyState = document.getElementById('empty-state');

                if (loadingIndicator) loadingIndicator.classList.remove('hidden');
                if (!append) {
                    if (itemsGrid) itemsGrid.classList.add('hidden');
                    if (emptyState) emptyState.classList.add('hidden');
                    // Нулираме pagination при нова директория
                    this.imageManager.pagination.currentPage = 1;
                }

                this.imageManager.pagination.loading = true;

                // Използваме фиксиран limit за консистентност
                const dynamicLimit = this.imageManager.pagination.limit;

                const userToken = BackendModule.config.userToken || '';
                const page = append ? this.imageManager.pagination.currentPage + 1 : 1;
                const url = `index.php?route=common/imagemanager&user_token=${userToken}&directory=${encodeURIComponent(directory)}&page=${page}&limit=${dynamicLimit}`;

                // Запазваме следващата страница за актуализация
                const nextPage = page;

                console.log('ImageManager: AJAX URL:', url);
                console.log('ImageManager: AJAX параметри:', {
                    directory: directory,
                    page: page,
                    limit: dynamicLimit,
                    staticLimit: this.imageManager.pagination.limit,
                    encoded_directory: encodeURIComponent(directory),
                    append: append
                });

                fetch(url, {
                        cache: 'no-store',
                        headers: { 'Cache-Control': 'no-cache, no-store, must-revalidate', 'Pragma': 'no-cache', 'Expires': '0' }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            if (append) {
                                this.appendDirectoryContents(data);
                            } else {
                                this.renderDirectoryContents(data);
                                this.updateBreadcrumb(data.breadcrumb);
                                this.imageManager.currentDirectory = directory;
                            }

                            // Актуализираме pagination данните
                            if (data.pagination) {
                                // При append операция, използваме nextPage вместо data.pagination.page
                                this.imageManager.pagination.currentPage = append ? nextPage : data.pagination.page;
                                this.imageManager.pagination.hasMore = data.pagination.has_more;

                                console.log('ImageManager: Pagination актуализирана:', {
                                    page: this.imageManager.pagination.currentPage,
                                    hasMore: data.pagination.has_more,
                                    totalImages: data.pagination.total_images,
                                    loadedImages: data.pagination.loaded_images,
                                    append: append,
                                    nextPage: nextPage
                                });
                            }
                        } else {
                            this.showAlert('error', data.error || 'Грешка при зареждане на директорията');
                        }
                    })
                    .catch(error => {
                        console.error('Грешка при зареждане на директория:', error);
                        this.showAlert('error', 'Възникна грешка при зареждане на директорията');
                    })
                    .finally(() => {
                        this.imageManager.pagination.loading = false;
                        if (loadingIndicator) loadingIndicator.classList.add('hidden');
                    });
            },

            /**
             * Рендериране на съдържанието на директория
             */
            renderDirectoryContents: function(data) {
                const itemsGrid = document.getElementById('items-grid');

                if (!itemsGrid) return;

                const gridContainer = itemsGrid.querySelector('.grid');
                if (!gridContainer) return;

                // Запазваме всички елементи за търсене
                this.imageManager.allItems = data.items || [];
                this.imageManager.filteredItems = [...this.imageManager.allItems];

                this.renderItems(this.imageManager.filteredItems);
            },

            /**
             * Добавя нови елементи към съществуващите (за infinite scroll)
             */
            appendDirectoryContents: function(data) {
                if (!data.items || data.items.length === 0) return;

                const gridContainer = document.getElementById('items-grid').querySelector('.grid');
                if (!gridContainer) return;

                // Филтрираме само изображенията (папките вече са заредени)
                const newImages = data.items.filter(item => item.type === 'image');

                console.log('ImageManager: Добавяне на нови изображения:', {
                    newImagesCount: newImages.length,
                    currentAllItemsCount: this.imageManager.allItems.length,
                    newImageNames: newImages.map(img => img.name)
                });

                newImages.forEach(item => {
                    const itemElement = this.createItemElement(item);
                    gridContainer.appendChild(itemElement);
                });

                // Актуализираме списъка с всички елементи
                this.imageManager.allItems = [...this.imageManager.allItems, ...newImages];
                this.imageManager.filteredItems = [...this.imageManager.filteredItems, ...newImages];

                console.log('ImageManager: След добавяне - общо елементи:', this.imageManager.allItems.length);

                // Актуализираме списъка с image елементи
                setTimeout(() => {
                    this.updateImageElementsList();
                }, 100);
            },

            /**
             * Рендира елементите в grid-а
             */
            renderItems: function(items) {
                const itemsGrid = document.getElementById('items-grid');
                const emptyState = document.getElementById('empty-state');
                const gridContainer = itemsGrid.querySelector('.grid');

                if (!gridContainer) return;

                gridContainer.innerHTML = '';

                if (items && items.length > 0) {
                    items.forEach(item => {
                        const itemElement = this.createItemElement(item);
                        gridContainer.appendChild(itemElement);

                        // Зареждане на thumbnail за изображения (cached или lazy loading)
                        if (item.type === 'image') {
                            this.loadImageThumbnail(item, itemElement);
                        }
                    });

                    itemsGrid.classList.remove('hidden');
                    if (emptyState) emptyState.classList.add('hidden');

                    // Актуализираме списъка с image елементи след рендиране
                    setTimeout(() => {
                        this.updateImageElementsList();
                    }, 100);
                } else {
                    itemsGrid.classList.add('hidden');
                    if (emptyState) emptyState.classList.remove('hidden');
                }
            },

            /**
             * Зарежда thumbnail за изображение (lazy loading)
             */
            loadImageThumbnail: function(item, itemElement) {
                const img = itemElement.querySelector('img');
                if (!img) return;

                // Ако има cached thumbnail, показваме го директно
                if (item.has_cache && item.thumb) {
                    img.src = item.thumb;
                    img.alt = item.name;
                    img.classList.remove('loading');
                    return;
                }

                // Ако няма URL за зареждане (cached изображения имат празен thumb_url), не правим нищо
                if (!item.thumb_url || item.thumb_url === '') return;

                // Показваме placeholder докато се зарежда
                img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0zNSA0MEg2NVY2MEgzNVY0MFoiIGZpbGw9IiNEMUQ1REIiLz4KPC9zdmc+';
                img.classList.add('loading');
                img.alt = 'Зареждане...';

                // Проверяваме дали URL-а е валиден
                if (!item.thumb_url || item.thumb_url === 'null' || item.thumb_url === null || item.thumb_url === '') {
                    console.warn('Невалиден thumb_url за изображение:', item.name, 'has_cache:', item.has_cache);
                    img.alt = 'Невалиден URL';
                    img.classList.remove('loading');
                    img.classList.add('error');
                    this.showWarningIcon(img);
                    return;
                }

                // Добавяме user_token към URL-а

                const url = new URL(item.thumb_url, window.location.origin + '/admin/');
                url.searchParams.set('user_token', this.getUserToken());

                // AJAX заявка за thumbnail
                fetch(url.toString(), {
                    cache: 'no-store',
                    headers: { 'Cache-Control': 'no-cache, no-store, must-revalidate' }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Thumbnail response за', item.name, ':', data);
                    if (data.success && data.thumbnail) {
                        img.src = data.thumbnail;
                        img.alt = item.name;
                        img.classList.remove('loading');
                    } else {
                        console.error('Грешка при зареждане на thumbnail:', data.error, 'show_warning_icon:', data.show_warning_icon);
                        img.alt = 'Грешка при зареждане';
                        img.classList.remove('loading');
                        img.classList.add('error');

                        // Ако има флаг за показване на warning икона
                        if (data.show_warning_icon) {
                            console.log('Показвам warning икона за', item.name);
                            this.showWarningIcon(img);
                        } else {
                            console.log('Няма show_warning_icon флаг за', item.name);
                        }
                    }
                })
                .catch(error => {
                    console.error('AJAX грешка при зареждане на thumbnail:', error);
                    img.alt = 'Грешка при зареждане';
                    img.classList.remove('loading');
                    img.classList.add('error');
                    this.showWarningIcon(img);
                });
            },

            /**
             * Получава user_token от сесията
             */
            getUserToken: function() {
                // 1. Проверяваме в глобалния обект
                if (window.user_token) {
                    return window.user_token;
                }

                // 2. Проверяваме в meta тагове
                const tokenMeta = document.querySelector('meta[name="user_token"]');
                if (tokenMeta) {
                    return tokenMeta.getAttribute('content');
                }

                // 3. Проверяваме в скрити input полета
                const tokenInput = document.querySelector('input[name="user_token"]');
                if (tokenInput) {
                    return tokenInput.value;
                }

                // 4. Проверяваме в URL параметрите
                const urlParams = new URLSearchParams(window.location.search);
                if (urlParams.has('user_token')) {
                    return urlParams.get('user_token');
                }

                // Ако не намерим никъде токена
                return '';
            },


            /**
             * Показва warning икона за изображения, които не могат да се заредят
             */
            showWarningIcon: function(imgElement) {
                // Заменяме изображението с червена warning икона (триъгълник с удивителен знак)
                imgElement.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTMwIDVMNTUgNTBINUwzMCA1WiIgZmlsbD0iI0ZCQkYyNCIgc3Ryb2tlPSIjRjU5RTBCIiBzdHJva2Utd2lkdGg9IjIiLz4KPHA+YXRoIGQ9Ik0zMCAyMFYzNSIgc3Ryb2tlPSIjRjU5RTBCIiBzdHJva2Utd2lkdGg9IjMiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIvPgo8Y2lyY2xlIGN4PSIzMCIgY3k9IjQyIiByPSIyIiBmaWxsPSIjRjU5RTBCIi8+Cjwvc3ZnPg==';
                imgElement.classList.add('warning-icon');
                imgElement.style.filter = 'none';
                imgElement.style.objectFit = 'contain';
                imgElement.style.padding = '10px';
            },

            /**
             * Създаване на елемент за файл/папка
             */
            createItemElement: function(item) {
                const element = document.createElement('div');

                if (item.type === 'directory') {
                    element.className = 'directory-item';
                    element.innerHTML = `
                        <div class="aspect-square rounded-lg overflow-hidden bg-gray-100 flex items-center justify-center">
                            <i class="ri-folder-line text-5xl text-gray-400"></i>
                        </div>
                        <p class="text-xs text-gray-600 mt-1 truncate" title="${item.name}">${item.name}</p>
                    `;

                    element.addEventListener('click', () => {
                        const newPath = this.imageManager.currentDirectory ?
                            `${this.imageManager.currentDirectory}/${item.path}` : item.path;
                        this.navigateToDirectory(newPath);
                    });
                } else {
                    element.className = 'image-item';
                    element.innerHTML = `
                        <div class="aspect-square rounded-lg overflow-hidden">
                            <img src="${item.thumb}" alt="${item.name}" class="w-full h-full object-cover">
                        </div>
                        <p class="text-xs text-gray-600 mt-1 truncate" title="${item.name}">${item.name}</p>
                    `;

                    // Добавяме data атрибути за селекция
                    element.dataset.path = item.path;
                    element.dataset.name = item.name;
                    element.dataset.thumb = item.thumb;

                    // Click event за селекция
                    element.addEventListener('click', (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        this.handleImageSelection(element, e);
                    });
                }

                return element;
            },

            /**
             * Обработка на селекция на изображение
             */
            handleImageSelection: function(element, event) {
                const path = element.dataset.path;
                const name = element.dataset.name;
                const thumb = element.dataset.thumb;

                // Получаваме всички image елементи в текущия grid
                this.updateImageElementsList();

                if (event && event.shiftKey && this.imageManager.lastSelectedElement) {
                    // Shift+Click - range selection
                    this.handleRangeSelection(element);
                } else if (event && event.ctrlKey) {
                    // Control+Click - toggle selection
                    this.toggleImageSelection(element, path, name, thumb);
                } else {
                    // Обикновен клик - изчистваме селекцията и селектираме само този елемент
                    this.clearSelection();
                    this.selectSingleImage(element, path, name, thumb);
                }

                // Запазваме последния селектиран елемент
                this.imageManager.lastSelectedElement = element;
                this.updateSelectionInfo();
            },

            /**
             * Актуализира списъка с всички image елементи
             */
            updateImageElementsList: function() {
                this.imageManager.allImageElements = Array.from(document.querySelectorAll('.image-item'));
            },

            /**
             * Обработва range selection (Shift+Click)
             */
            handleRangeSelection: function(currentElement) {
                const lastIndex = this.imageManager.allImageElements.indexOf(this.imageManager.lastSelectedElement);
                const currentIndex = this.imageManager.allImageElements.indexOf(currentElement);

                if (lastIndex === -1 || currentIndex === -1) return;

                const startIndex = Math.min(lastIndex, currentIndex);
                const endIndex = Math.max(lastIndex, currentIndex);

                // Селектираме всички елементи в диапазона
                for (let i = startIndex; i <= endIndex; i++) {
                    const element = this.imageManager.allImageElements[i];
                    const path = element.dataset.path;
                    const name = element.dataset.name;
                    const thumb = element.dataset.thumb;

                    if (!this.imageManager.selectedImages.find(img => img.path === path)) {
                        this.imageManager.selectedImages.push({ path, name, thumb });
                        element.classList.add('selected');
                    }
                }
            },

            /**
             * Toggle селекция на изображение (Control+Click)
             */
            toggleImageSelection: function(element, path, name, thumb) {
                const isSelected = element.classList.contains('selected');

                if (isSelected) {
                    // Премахваме от селекцията
                    this.imageManager.selectedImages = this.imageManager.selectedImages.filter(img => img.path !== path);
                    element.classList.remove('selected');
                } else {
                    // Добавяме към селекцията
                    this.imageManager.selectedImages.push({ path, name, thumb });
                    element.classList.add('selected');
                }
            },

            /**
             * Селектира единично изображение
             */
            selectSingleImage: function(element, path, name, thumb) {
                this.imageManager.selectedImages.push({ path, name, thumb });
                element.classList.add('selected');
            },

            /**
             * Обработва търсенето с debouncing
             */
            handleSearchWithDebounce: function(query) {
                // Изчистваме предишния timeout
                if (this.imageManager.searchTimeout) {
                    clearTimeout(this.imageManager.searchTimeout);
                }

                // Задаваме нов timeout за 500ms
                this.imageManager.searchTimeout = setTimeout(() => {
                    this.handleSearch(query);
                }, 500);
            },

            /**
             * Обработва търсенето
             */
            handleSearch: function(query) {
                this.imageManager.searchQuery = query.toLowerCase().trim();

                const clearButton = document.getElementById('clear-search');
                if (clearButton) {
                    if (this.imageManager.searchQuery) {
                        clearButton.classList.remove('hidden');
                    } else {
                        clearButton.classList.add('hidden');
                    }
                }

                if (!this.imageManager.searchQuery) {
                    // Показваме всички елементи
                    this.imageManager.filteredItems = [...this.imageManager.allItems];
                } else {
                    // Филтрираме елементите
                    this.imageManager.filteredItems = this.imageManager.allItems.filter(item => {
                        return item.name.toLowerCase().includes(this.imageManager.searchQuery);
                    });

                    // Добавяме рекурсивно търсене в подпапки
                    if (this.imageManager.searchQuery) {
                        this.performRecursiveSearch();
                    }
                }

                this.renderItems(this.imageManager.filteredItems);
            },

            /**
             * Изчиства търсенето
             */
            clearSearch: function() {
                // Изчистваме timeout ако има такъв
                if (this.imageManager.searchTimeout) {
                    clearTimeout(this.imageManager.searchTimeout);
                    this.imageManager.searchTimeout = null;
                }

                const searchInput = document.getElementById('search-images');
                const clearButton = document.getElementById('clear-search');

                if (searchInput) searchInput.value = '';
                if (clearButton) clearButton.classList.add('hidden');

                this.imageManager.searchQuery = '';
                this.imageManager.filteredItems = [...this.imageManager.allItems];
                this.renderItems(this.imageManager.filteredItems);
            },

            /**
             * Извършва рекурсивно търсене в подпапки
             */
            performRecursiveSearch: function() {
                // За рекурсивното търсене ще направим AJAX заявка към сървъра
                const userToken = BackendModule.config.userToken || '';
                const url = `index.php?route=common/imagemanager/search&user_token=${userToken}&query=${encodeURIComponent(this.imageManager.searchQuery)}&directory=${encodeURIComponent(this.imageManager.currentDirectory)}`;

                fetch(url, {
                        cache: 'no-store',
                        headers: { 'Cache-Control': 'no-cache, no-store, must-revalidate', 'Pragma': 'no-cache', 'Expires': '0' }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.items) {
                            // Комбинираме резултатите от рекурсивното търсене
                            const recursiveResults = data.items.filter(item => {
                                // Избягваме дублиране на елементи от текущата директория
                                return !this.imageManager.filteredItems.find(existing => existing.path === item.path);
                            });

                            this.imageManager.filteredItems = [...this.imageManager.filteredItems, ...recursiveResults];
                            this.renderItems(this.imageManager.filteredItems);
                        }
                    })
                    .catch(error => {
                        console.error('Грешка при рекурсивно търсене:', error);
                    });
            },

            /**
             * Актуализиране на информацията за селекцията
             */
            updateSelectionInfo: function() {
                const selectedCount = document.getElementById('selected-count');
                const addSelectedBtn = document.getElementById('add-selected');

                if (selectedCount) {
                    selectedCount.textContent = this.imageManager.selectedImages.length;
                }

                if (addSelectedBtn) {
                    addSelectedBtn.disabled = this.imageManager.selectedImages.length === 0;
                }
            },

            /**
             * Изчистване на селекцията
             */
            clearSelection: function() {
                this.imageManager.selectedImages = [];

                // Премахваме визуалната селекция
                document.querySelectorAll('.image-item.selected').forEach(item => {
                    item.classList.remove('selected');
                });

                this.updateSelectionInfo();
            },

            /**
             * Добавяне на избраните изображения към продукта
             */
            addSelectedImages: function() {
                if (this.imageManager.selectedImages.length === 0) return;

                this.imageManager.selectedImages.forEach(image => {
                    this.addImageToProduct(image.path, image.thumb);
                });

                this.showAlert('success', `Добавени ${this.imageManager.selectedImages.length} изображения към продукта`);
                this.closeImageManager();
            },

            /**
             * Навигация нагоре в директорията
             */
            navigateUp: function() {
                const currentDir = this.imageManager.currentDirectory;

                console.log('ImageManager: navigateUp() извикан с currentDir:', currentDir);

                // Ако сме в root директорията, не можем да отидем по-нагоре
                if (!currentDir || currentDir === '') {
                    console.log('ImageManager: Вече сме в root директорията');
                    return;
                }

                const parts = currentDir.split('/').filter(part => part !== '');
                console.log('ImageManager: Части на пътя:', parts);

                if (parts.length > 1) {
                    // Премахваме последната част
                    parts.pop();
                    const parentDir = parts.join('/');
                    console.log('ImageManager: Навигация към родителска директория:', parentDir);
                    this.navigateToDirectory(parentDir);
                } else {
                    // Отиваме в root директорията
                    console.log('ImageManager: Навигация към root директория');
                    this.navigateToDirectory('');
                }
            },

            /**
             * Навигация към конкретна директория
             */
            navigateToDirectory: function(directory) {
                this.clearSelection();
                this.loadDirectoryContents(directory);
            },

            /**
             * Обновяване на мениджъра
             */
            refreshManager: function() {
                this.loadDirectoryContents(this.imageManager.currentDirectory);
            },

            /**
             * Актуализиране на breadcrumb навигацията
             */
            updateBreadcrumb: function(breadcrumbData) {
                const breadcrumbNav = document.getElementById('breadcrumb-nav');
                if (!breadcrumbNav || !breadcrumbData) return;

                breadcrumbNav.innerHTML = '';

                breadcrumbData.forEach((item, index) => {
                    const isLast = index === breadcrumbData.length - 1;

                    const breadcrumbItem = document.createElement('span');
                    breadcrumbItem.className = `breadcrumb-item ${isLast ? 'active' : ''}`;
                    breadcrumbItem.textContent = item.name;
                    breadcrumbItem.dataset.path = item.path;

                    if (!isLast) {
                        breadcrumbItem.addEventListener('click', () => {
                            this.navigateToDirectory(item.path);
                        });
                    }

                    breadcrumbNav.appendChild(breadcrumbItem);

                    // Добавяме разделител ако не е последният
                    if (!isLast) {
                        const separator = document.createElement('span');
                        separator.className = 'text-gray-400 mx-2';
                        separator.innerHTML = '<i class="ri-arrow-right-s-line"></i>';
                        breadcrumbNav.appendChild(separator);
                    }
                });
            },

            /**
             * Качване на файлове в мениджъра
             */
            uploadFilesToManager: function(files) {
                if (!files || files.length === 0) return;

                // Показваме progress modal
                this.showUploadProgress();

                const userToken = BackendModule.config.userToken || '';
                const directory = this.imageManager.currentDirectory || '';

                const formData = new FormData();
                files.forEach(file => {
                    formData.append('files[]', file);
                });

                fetch(`index.php?route=common/imagemanager/upload&user_token=${userToken}&directory=${encodeURIComponent(directory)}`, {
                    method: 'POST',
                    body: formData,
                    cache: 'no-store',
                    headers: { 'Cache-Control': 'no-cache, no-store, must-revalidate', 'Pragma': 'no-cache', 'Expires': '0' }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        this.showAlert('success', data.message || 'Файловете са качени успешно');
                        this.refreshManager();
                    } else {
                        this.showAlert('error', data.error || 'Грешка при качване на файловете');
                    }
                })
                .catch(error => {
                    console.error('Грешка при качване:', error);
                    this.showAlert('error', 'Възникна грешка при качване на файловете');
                })
                .finally(() => {
                    this.hideUploadProgress();
                });
            },

            /**
             * Показване на progress за качване
             */
            showUploadProgress: function() {
                const modal = document.getElementById('upload-progress-modal');
                if (modal) {
                    modal.classList.remove('hidden');
                }
            },

            /**
             * Скриване на progress за качване
             */
            hideUploadProgress: function() {
                const modal = document.getElementById('upload-progress-modal');
                if (modal) {
                    modal.classList.add('hidden');
                }
            },

            /**
             * Инициализира infinite scroll функционалността
             */
            initInfiniteScroll: function() {
                const modal = document.getElementById('image-manager-modal');
                if (!modal) return;

                // Търсим правилния scroll контейнер
                const contentArea = modal.querySelector('.max-h-96.overflow-y-auto') ||
                                  modal.querySelector('[style*="max-height"]') ||
                                  modal.querySelector('.overflow-y-auto');

                if (!contentArea) {
                    console.warn('ImageManager: Не е намерен scroll контейнер за infinite scroll');
                    return;
                }

                console.log('ImageManager: Infinite scroll инициализиран за контейнер:', contentArea);

                contentArea.addEventListener('scroll', () => {
                    // Проверяваме дали сме близо до края на контейнера
                    const scrollTop = contentArea.scrollTop;
                    const scrollHeight = contentArea.scrollHeight;
                    const clientHeight = contentArea.clientHeight;

                    // Зареждаме нови елементи когато сме на 80% от скрола (по-рано)
                    const scrollPercentage = (scrollTop + clientHeight) / scrollHeight;

                    console.log('Scroll:', {
                        scrollTop,
                        scrollHeight,
                        clientHeight,
                        percentage: Math.round(scrollPercentage * 100) + '%',
                        hasMore: this.imageManager.pagination.hasMore,
                        loading: this.imageManager.pagination.loading,
                        searchActive: !!this.imageManager.searchQuery
                    });

                    if (scrollPercentage >= 0.8 &&
                        this.imageManager.pagination.hasMore &&
                        !this.imageManager.pagination.loading &&
                        !this.imageManager.searchQuery) { // Не зареждаме при активно търсене

                        console.log('ImageManager: Зареждане на повече изображения...');
                        this.loadMoreImages();
                    }
                });
            },

            /**
             * Зарежда повече изображения (infinite scroll)
             */
            loadMoreImages: function() {
                if (this.imageManager.pagination.loading || !this.imageManager.pagination.hasMore) {
                    console.log('ImageManager: Не зареждаме повече изображения:', {
                        loading: this.imageManager.pagination.loading,
                        hasMore: this.imageManager.pagination.hasMore
                    });
                    return;
                }

                console.log('ImageManager: Зареждане на следваща страница:', this.imageManager.pagination.currentPage + 1);
                this.loadDirectoryContents(this.imageManager.currentDirectory, true);
            }
        });
    }
})();
