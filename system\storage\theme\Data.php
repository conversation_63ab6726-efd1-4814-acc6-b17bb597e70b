<?php

namespace Theme25;

/**
 * Singleton клас за достъп до конфигурационни настройки от .env файла
 * с fallback към константи
 */
class Data {
    /**
     * Единствена инстанция на класа
     *
     * @var Data
     */
    private static $instance = null;

    /**
     * Обект за зареждане на .env файла
     *
     * @var EnvLoader
     */
    private $envLoader;

    /**
     * Кеширани стойности на настройките
     *
     * @var array
     */
    private $cache = [];

    /**
     * Частен конструктор за предотвратяване на директно създаване на обекти
     */
    private function __construct() {
        $envPath = defined('DIR_THEME') ? DIR_THEME . '.env' : __DIR__ . '/.env';
        $this->envLoader = new EnvLoader($envPath);
    }

    /**
     * Предотвратяване на клониране на обекта
     */
    private function __clone() {
    }

    /**
     * Предотвратяване на десериализация на обекта
     */
    public function __wakeup() {
        throw new \Exception("Не може да се десериализира singleton");
    }

    /**
     * Връща единствената инстанция на класа
     *
     * @return Data
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }

        return self::$instance;
    }

    /**
     * Връща стойност от .env файла или константа, ако стойността не е зададена
     *
     * @param string $key Ключ в .env файла
     * @param string $constantName Име на константата за fallback
     * @param mixed $default Стойност по подразбиране, ако нито ключът, нито константата съществуват
     * @return mixed
     */
    public function get($key, $constantName = null, $default = null) {
        // Проверка дали стойността е кеширана
        $cacheKey = $key . '_' . $constantName;
        if (isset($this->cache[$cacheKey])) {
            return $this->cache[$cacheKey];
        }

        // Опит за извличане от .env файла
        $value = $this->envLoader->get($key, null);

        // Ако стойността е празна и е подадено име на константа, опит за извличане от константата
        if (($value === null || $value === '') && $constantName !== null && defined($constantName)) {
            $value = constant($constantName);
        }

        // Ако стойността все още е празна, връщане на стойността по подразбиране
        if ($value === null) {
            $value = $default;
        }

        // Кеширане на стойността
        $this->cache[$cacheKey] = $value;

        return $value;
    }

    /**
     * Връща масив от стойности, разделени със запетая
     *
     * @param string $key Ключ в .env файла
     * @param array $default Масив по подразбиране, ако ключът не съществува
     * @return array
     */
    public function getArray($key, $default = []) {
        // Проверка дали стойността е кеширана
        $cacheKey = 'array_' . $key;
        if (isset($this->cache[$cacheKey])) {
            return $this->cache[$cacheKey];
        }

        // Опит за извличане от .env файла
        $array = $this->envLoader->getArray($key);

        // Ако масивът е празен, връщане на масива по подразбиране
        if (empty($array)) {
            $array = $default;
        }

        // Кеширане на стойността
        $this->cache[$cacheKey] = $array;

        return $array;
    }

    /**
     * Проверява дали ключ съществува в .env файла
     *
     * @param string $key Ключ в .env файла
     * @return bool
     */
    public function has($key) {
        return $this->envLoader->has($key);
    }

    /**
     * Връща всички настройки от .env файла
     *
     * @return array
     */
    public function all() {
        return $this->envLoader->all();
    }

    /**
     * Връща пътя към сървъра с изображения
     * Ако IMAGE_SERVER_PATH е зададен в .env файла, връща тази стойност
     * В противен случай връща стойността на константата DIR_IMAGE
     *
     * @return string
     */
    public function getImageServerPath($theme_path = true) {
        if(!$theme_path) return DIR_IMAGE;
        return $this->get('IMAGE_SERVER_PATH', 'DIR_IMAGE', '');
    }

    public function getImageCatalogPath($theme_path = true) {
        return $theme_path ? $this->getImageServerPath() . 'catalog/' : DIR_IMAGE . 'catalog/';
    }

    public function getImageWebUrl($theme_path = true) {
        $standardUrl = HTTPS_CATALOG . 'image/';
        if(!$theme_path) return $standardUrl;
        return $this->get('IMAGE_WEB_URL', null, $standardUrl);
    }

    /**
     * Връща URL към сървъра с изображения
     * Алиас на getImageWebUrl за съвместимост
     *
     * @param bool $theme_path Дали да използва theme пътя
     * @return string
     */
    public function getImageServerUrl($theme_path = true) {
        return $this->getImageWebUrl($theme_path);
    }

    public function getNoImageFile($theme_path = true) {
        $file = $this->get('NO_IMAGE_FILE', null, 'no_image.png');
        $imagePath = $this->getImageServerPath($theme_path);
        if(file_exists($imagePath . $file)) {
            return $file;
        }
        return false;
    }

    /**
     * Връща дали е разрешена втората база данни
     *
     * @return bool
     */
    public function isSecondDbEnabled() {
        return (bool)$this->get('SECOND_DB_ENABLED', null, false);
    }

    /**
     * Връща идентификатора на втората база данни
     *
     * @return string
     */
    public function getSecondDbIdentifier() {
        return $this->get('SECOND_DB_IDENTIFIER', null, 'second_db');
    }

    /**
     * Връща префикса на таблиците за втората база данни
     *
     * @return string
     */
    public function getSecondDbPrefix() {
        return $this->get('SECOND_DB_PREFIX', null, 'oc_');
    }

    /**
     * Връща масив с модулите, които използват втората база данни
     *
     * @return array
     */
    public function getSecondDbModules() {
        return $this->getArray('SECOND_DB_MODULES', []);
    }

    /**
     * Връща дали е разрешено дебъгването на втората база данни
     *
     * @return bool
     */
    public function isSecondDbDebugEnabled() {
        return (bool)$this->get('SECOND_DB_DEBUG', null, false);
    }

    /**
     * Връща името на темата
     *
     * @return string
     */
    public function getThemeName() {
        return $this->get('THEME_NAME', null, 'Theme25');
    }

    /**
     * Изчиства кеша на настройките
     *
     * @return void
     */
    public function clearCache() {
        $this->cache = [];
    }

    /**
     * Презарежда .env файла
     *
     * @return void
     */
    public function reload() {
        $envPath = defined('DIR_THEME') ? DIR_THEME . '.env' : __DIR__ . '/.env';
        $this->envLoader = new EnvLoader($envPath);
        $this->clearCache();
    }
}

