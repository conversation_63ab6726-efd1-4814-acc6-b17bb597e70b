<?php

namespace Theme25\Backend\Controller\Common\ImageManager;

class Validation extends \Theme25\ControllerSubMethods {

    /**
     * Получава целевата директория от заявката
     * 
     * @return string Път до директорията
     */
    public function getTargetDirectory() {
        $requestDirectory = ltrim(urldecode($this->requestGet('directory', '')), '/');

        if ($requestDirectory) {
            return rtrim( ThemeData()->getImageCatalogPath() . $requestDirectory, '/');
        }
        
        return ThemeData()->getImageCatalogPath();
    }
    
    /**
     * Проверява дали директорията е валидна и в рамките на catalog папката
     *
     * @param string $directory Път до директорията
     * @return bool
     */
    public function isValidDirectory($directory) {
        if (!is_dir($directory)) {
            return false;
        }

        $realPath = realpath($directory);
        if ($realPath === false) {
            // Ако realpath() не работи, опитваме се с директна проверка
            $realPath = $directory;
        }

        $realPath = str_replace('\\', '/', $realPath);
        $catalogPath = str_replace('\\', '/', ThemeData()->getImageCatalogPath());

        // Премахваме trailing slash за правилно сравнение
        $realPath = trim($realPath, '/');
        $catalogPath = trim($catalogPath, '/');

        return substr($realPath, 0, strlen($catalogPath)) === $catalogPath;
    }
    
    /**
     * Проверява права на достъп за операции с файлове
     * 
     * @return array Резултат от проверката
     */
    public function validatePermissions() {
        // if (!$this->hasPermission('modify', 'common/filemanager')) {
        //     return [
        //         'valid' => false,
        //         'error' => $this->getLanguageText('error_permission')
        //     ];
        // }
        
        return ['valid' => true];
    }
    
    /**
     * Валидира качен файл
     * 
     * @param array $file Данни за файла
     * @return array Резултат от валидацията
     */
    public function validateUploadedFile($file) {
        $filename = basename(html_entity_decode($file['name'], ENT_QUOTES, 'UTF-8'));
        
        // Проверка на дължината на името
        if ((utf8_strlen($filename) < 3) || (utf8_strlen($filename) > 255)) {
            return [
                'valid' => false,
                'error' => 'Невалидно име на файл: ' . $filename
            ];
        }
        
        // Проверка на разширението
        $allowedExtensions = ['jpg', 'jpeg', 'gif', 'png'];
        $extension = utf8_strtolower(utf8_substr(strrchr($filename, '.'), 1));
        
        if (!in_array($extension, $allowedExtensions)) {
            return [
                'valid' => false,
                'error' => 'Неподдържан формат на файл: ' . $filename
            ];
        }
        
        // Проверка на MIME типа
        $allowedMimes = ['image/jpeg', 'image/pjpeg', 'image/png', 'image/x-png', 'image/gif'];
        
        if (!in_array($file['type'], $allowedMimes)) {
            return [
                'valid' => false,
                'error' => 'Невалиден MIME тип: ' . $filename
            ];
        }
        
        // Проверка на размера (5MB лимит)
        if ($file['size'] > 5 * 1024 * 1024) {
            return [
                'valid' => false,
                'error' => 'Файлът е твърде голям: ' . $filename
            ];
        }
        
        return ['valid' => true];
    }
    
    /**
     * Валидира директория и права на достъп
     * 
     * @return array Резултат от валидацията
     */
    public function validateDirectoryAccess() {
        // Проверка за права на достъп
        $permissionCheck = $this->validatePermissions();
        if (!$permissionCheck['valid']) {
            return $permissionCheck;
        }
        
        // Получаване на директорията
        $directory = $this->getTargetDirectory();

        // Проверка за валидност на директорията
        if (!$this->isValidDirectory($directory)) {
            return [
                'valid' => false,
                'error' => 'Невалидна директория',
                'directory' => null
            ];
        }
        
        return [
            'valid' => true,
            'directory' => $directory
        ];
    }
    
    /**
     * Валидира множество файлове за качване
     * 
     * @param array $files Масив с файлове
     * @return array Резултат от валидацията
     */
    public function validateMultipleFiles($files) {
        $validFiles = [];
        $errors = [];
        
        if (empty($files)) {
            return [
                'valid' => false,
                'error' => 'Няма избрани файлове за качване',
                'validFiles' => [],
                'errors' => []
            ];
        }
        
        // Обработка на множество файлове
        if (is_array($files['name'])) {
            for ($i = 0; $i < count($files['name']); $i++) {
                if ($files['error'][$i] == UPLOAD_ERR_OK) {
                    $fileData = [
                        'name' => $files['name'][$i],
                        'type' => $files['type'][$i],
                        'tmp_name' => $files['tmp_name'][$i],
                        'error' => $files['error'][$i],
                        'size' => $files['size'][$i]
                    ];
                    
                    $validation = $this->validateUploadedFile($fileData);
                    if ($validation['valid']) {
                        $validFiles[] = $fileData;
                    } else {
                        $errors[] = $validation['error'];
                    }
                }
            }
        } else {
            // Обработка на единичен файл
            if ($files['error'] == UPLOAD_ERR_OK) {
                $validation = $this->validateUploadedFile($files);
                if ($validation['valid']) {
                    $validFiles[] = $files;
                } else {
                    $errors[] = $validation['error'];
                }
            }
        }
        
        return [
            'valid' => !empty($validFiles),
            'validFiles' => $validFiles,
            'errors' => $errors,
            'totalFiles' => count($validFiles),
            'errorCount' => count($errors)
        ];
    }

}
