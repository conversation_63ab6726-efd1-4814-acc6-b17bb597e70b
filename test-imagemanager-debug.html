<!DOCTYPE html>
<html lang="bg">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ImageManager Debug Test</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        .debug-container {
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .test-item {
            border: 1px solid #ddd;
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .image-preview {
            width: 100px;
            height: 100px;
            border: 1px solid #ccc;
            margin: 10px 0;
        }
        .loading {
            opacity: 0.6;
            border: 2px dashed #999;
        }
        .error {
            border: 2px solid #f00;
        }
        .warning-icon {
            background-color: #fff3cd;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>ImageManager Debug Test</h1>
        
        <div class="test-item">
            <h3>Тест 1: Изображение без cache</h3>
            <div id="test1-container"></div>
            <button onclick="testNoCacheImage()">Тествай изображение без cache</button>
        </div>
        
        <div class="test-item">
            <h3>Тест 2: Изображение с cache</h3>
            <div id="test2-container"></div>
            <button onclick="testCachedImage()">Тествай cached изображение</button>
        </div>
        
        <div class="test-item">
            <h3>Console Log:</h3>
            <div id="console-log" class="log"></div>
            <button onclick="clearLog()">Изчисти лога</button>
        </div>
    </div>

    <script>
        // Симулация на ImageManager логика
        const TestImageManager = {
            getUserToken: function() {
                return 'test_token_123';
            },
            
            showWarningIcon: function(imgElement) {
                imgElement.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTMwIDVMNTUgNTBINUwzMCA1WiIgZmlsbD0iI0ZCQkYyNCIgc3Ryb2tlPSIjRjU5RTBCIiBzdHJva2Utd2lkdGg9IjIiLz4KPHBhdGggZD0iTTMwIDIwVjM1IiBzdHJva2U9IiNGNTlFMEIiIHN0cm9rZS13aWR0aD0iMyIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIi8+CjxjaXJjbGUgY3g9IjMwIiBjeT0iNDIiIHI9IjIiIGZpbGw9IiNGNTlFMEIiLz4KPC9zdmc+';
                imgElement.classList.add('warning-icon');
                imgElement.style.filter = 'none';
                imgElement.style.objectFit = 'contain';
                imgElement.style.padding = '10px';
                log('Warning икона показана');
            },
            
            loadImageThumbnail: function(item, itemElement) {
                const img = itemElement.querySelector('img');
                if (!img) return;

                log('loadImageThumbnail извикан за: ' + item.name + ', has_cache: ' + item.has_cache + ', thumb_url: ' + item.thumb_url);

                // Ако има cached thumbnail, показваме го директно
                if (item.has_cache && item.thumb) {
                    log('Показвам cached thumbnail за: ' + item.name);
                    img.src = item.thumb;
                    img.alt = item.name;
                    img.classList.remove('loading');
                    return;
                }

                // За изображения без cache трябва да имаме thumb_url за AJAX заявка
                if (!item.thumb_url || item.thumb_url === '') {
                    log('ГРЕШКА: Няма thumb_url за изображение без cache: ' + item.name);
                    img.alt = 'Няма URL за зареждане';
                    img.classList.remove('loading');
                    img.classList.add('error');
                    this.showWarningIcon(img);
                    return;
                }

                log('Започвам AJAX заявка за: ' + item.name + ' към URL: ' + item.thumb_url);

                // Показваме placeholder докато се зарежда
                img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0zNSA0MEg2NVY2MEgzNVY0MFoiIGZpbGw9IiNEMUQ1REIiLz4KPC9zdmc+';
                img.classList.add('loading');
                img.alt = 'Зареждане...';

                // Симулация на AJAX заявка
                setTimeout(() => {
                    log('Симулирам AJAX отговор за: ' + item.name);
                    
                    // Симулираме грешка за тест
                    if (item.name === 'test-error.jpg') {
                        log('Симулирам AJAX грешка за: ' + item.name);
                        img.alt = 'Грешка при зареждане';
                        img.classList.remove('loading');
                        img.classList.add('error');
                        this.showWarningIcon(img);
                    } else {
                        log('Симулирам успешен отговор за: ' + item.name);
                        img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjNEZBRjUwIi8+CjxwYXRoIGQ9Ik00MCA1MEw1MCA2MEw3MCA0MCIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIzIiBmaWxsPSJub25lIi8+CjwvdXN2Zz4=';
                        img.alt = item.name;
                        img.classList.remove('loading');
                    }
                }, 1000);
            }
        };

        function log(message) {
            const logDiv = document.getElementById('console-log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += '[' + timestamp + '] ' + message + '<br>';
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('console-log').innerHTML = '';
        }

        function testNoCacheImage() {
            const container = document.getElementById('test1-container');
            container.innerHTML = '';
            
            const item = {
                type: 'image',
                name: 'test-no-cache.jpg',
                has_cache: false,
                thumb: 'test-no-cache.jpg',
                thumb_url: '/admin/index.php?route=common/imagemanager/thumbnail&path=test-no-cache.jpg'
            };
            
            const element = document.createElement('div');
            element.innerHTML = '<img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0zNSA0MEg2NVY2MEgzNVY0MFoiIGZpbGw9IiNEMUQ1REIiLz4KPC9zdmc+" alt="' + item.name + '" class="image-preview loading">';
            container.appendChild(element);
            
            log('Тествам изображение без cache: ' + item.name);
            TestImageManager.loadImageThumbnail(item, element);
        }

        function testCachedImage() {
            const container = document.getElementById('test2-container');
            container.innerHTML = '';
            
            const item = {
                type: 'image',
                name: 'test-cached.jpg',
                has_cache: true,
                thumb: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjMjE5NkYzIi8+CjxwYXRoIGQ9Ik00MCA1MEw1MCA2MEw3MCA0MCIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIzIiBmaWxsPSJub25lIi8+PC9zdmc+',
                thumb_url: ''
            };
            
            const element = document.createElement('div');
            element.innerHTML = '<img src="' + item.thumb + '" alt="' + item.name + '" class="image-preview">';
            container.appendChild(element);
            
            log('Тествам cached изображение: ' + item.name);
            TestImageManager.loadImageThumbnail(item, element);
        }
    </script>
</body>
</html>
